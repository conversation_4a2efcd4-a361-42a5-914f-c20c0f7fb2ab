# API Gateway Configuration
API_GATEWAY_URL=http://localhost:3000
API_BASE_URL=http://localhost:3000/api

# WebSocket Configuration
WEBSOCKET_URL=http://localhost:3000
WEBSOCKET_TIMEOUT=10000

# Test Configuration
TEST_TIMEOUT=60000
TEST_USER_PASSWORD=TestPassword123!
TEST_CLEANUP_ENABLED=true

# Database Configuration (for cleanup)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_db
DB_USER=atma_user
DB_PASSWORD=atma_password

# Logging
LOG_LEVEL=info
LOG_FILE=logs/e2e-test.log

# Test Data
RANDOM_EMAIL_DOMAIN=test.atma.local
TEST_ASSESSMENT_NAME=AI-Driven Talent Mapping
